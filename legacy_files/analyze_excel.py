#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

def analyze_excel():
    # 读取Excel文件
    df = pd.read_excel('Aida-node_88294559_20250812.xlsx')
    
    print('Excel文件基本信息：')
    print(f'列名: {df.columns.tolist()}')
    print(f'数据形状: {df.shape}')
    print()
    
    # 查看第一个prompt内容
    if len(df) > 0 and pd.notna(df['prompt'].iloc[0]):
        prompt_sample = str(df['prompt'].iloc[0])
        print('完整Prompt内容（第一条）：')
        print(prompt_sample)
        print('\n' + '='*80 + '\n')
        
        # 检查关键分隔符
        keywords = [
            '可能用到的业务知识如下：',
            '可能用到的方案列表如下：', 
            '系统信号如下：',
            '客服和商家或者骑手的最近一次通话记录如下:',
            '对话历史如下：'
        ]
        
        print('关键字段位置检查：')
        for keyword in keywords:
            pos = prompt_sample.find(keyword)
            if pos != -1:
                print(f'✓ 找到 "{keyword}" 在位置: {pos}')
            else:
                print(f'✗ 未找到: "{keyword}"')
        
        # 检查¥¥符号
        yuan_count = prompt_sample.count('¥¥')
        print(f'\n"¥¥"符号数量: {yuan_count}')
        
        # 如果找到¥¥，展示一些示例
        if yuan_count > 0:
            print('\n¥¥符号示例位置：')
            start = 0
            for i in range(min(3, yuan_count)):  # 最多显示前3个
                pos = prompt_sample.find('¥¥', start)
                if pos != -1:
                    context = prompt_sample[max(0, pos-20):pos+22]
                    print(f'  位置 {pos}: ...{context}...')
                    start = pos + 1

if __name__ == '__main__':
    analyze_excel()