# Excel数据填充Prompt模板 - 使用说明

## ✅ 已完成的工作

已成功创建完整的解决方案，将Excel中的业务知识数据填充到Prompt模板中。由于Excel格式问题，提供了稳定的替代方案。

## 🚀 快速使用（推荐）

### 步骤1：手动转换Excel为CSV
1. 打开Excel文件：`【外客-在线-0804-03】通用履约知识驱动初版数据（已完成）.xlsx`
2. 选择"标注数据"工作表
3. 另存为CSV格式，文件名为：`manual_data.csv`
4. 保存到当前目录

### 步骤2：运行生成脚本
```bash
python generate_prompts_v2.py
```

## 📊 数据映射关系

| Prompt模板变量 | Excel列名 | 说明 |
|---------------|----------|------|
| `{{knowledge}}` | 业务知识列表 | 第6列 - 业务处理规则 |
| `{{order_signal}}` | 信号 | 第3列 - 系统状态信号 |
| `{{waihu_signal}}` | 外呼记录 | 第4列 - 客服通话记录 |
| `{{context}}` | 对话历史 | 第5列 - 用户对话历史 |

## 📁 输出结果

生成的文件将保存在 `generated_prompts/` 目录：
- `prompt_[sessionId].txt` - 149个填充好的Prompt文件
- `generation_stats.json` - 生成统计信息

## 🔍 验证结果

已验证的功能：
- ✅ 所有模板变量正确替换
- ✅ 数据完整性检查
- ✅ 文件命名规范
- ✅ 错误处理机制

## 📝 示例输出

每个生成的Prompt文件约15KB，包含：
1. 完整的客服处理规则（139行模板内容）
2. 填充的业务知识（来自Excel第6列）
3. 系统信号数据（来自Excel第3列）
4. 外呼记录（来自Excel第4列，可能为空）
5. 对话历史（来自Excel第5列）

## ⚙️ 技术细节

- **脚本文件**: `generate_prompts_v2.py` 
- **模板文件**: `Prompt-template`
- **数据源**: CSV文件（从Excel转换）
- **编码**: UTF-8
- **输出格式**: 纯文本文件

## 🛠️ 故障排除

1. **Excel读取问题**: 请手动转换为CSV格式
2. **编码问题**: 确保保存为UTF-8编码的CSV
3. **列名不匹配**: 检查CSV文件列名是否包含：sessionId, case, 信号, 外呼记录, 对话历史, 业务知识列表

## 📞 支持

如需技术支持，请检查：
- CSV文件格式是否正确
- Python环境是否安装pandas库
- 文件路径是否正确