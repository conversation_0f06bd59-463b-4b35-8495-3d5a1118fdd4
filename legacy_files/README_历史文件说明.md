# 历史文件说明

## 📁 目录用途

这个目录包含了项目中的历史文件和早期版本的脚本，这些文件在开发新的Prompt解析器之前就已经存在。

## 📋 文件清单

### Python脚本
| 文件名 | 创建时间 | 用途 | 状态 |
|--------|----------|------|------|
| `add_prompts_to_excel.py` | 2025-08-11 17:59 | 向Excel添加Prompt的脚本 | 历史版本 |
| `analyze_excel.py` | 2025-08-12 12:07 | Excel分析脚本 | 历史版本 |
| `generate_prompts_v2.py` | 2025-08-11 17:59 | Prompt生成脚本v2版本 | 历史版本 |

### Excel文件
| 文件名 | 大小 | 创建时间 | 用途 | 状态 |
|--------|------|----------|------|------|
| `Aida-node_88294559_20250812.xlsx` | 83KB | 2025-08-12 11:45 | 原始输入文件 | 已移至prompt_parser |
| `待解析1.xlsx` | 2.8MB | 2025-08-07 20:02 | 早期待解析文件 | 历史文件 |
| `待解析1.csv` | 289KB | 2025-08-07 20:05 | CSV格式的待解析文件 | 历史文件 |
| `待解析1_含最终Prompt.xlsx` | 714KB | 2025-08-11 18:00 | 包含最终Prompt的处理结果 | 历史版本 |

### 其他文件和目录
| 名称 | 类型 | 用途 | 状态 |
|------|------|------|------|
| `generated_prompts/` | 目录 | 包含生成的Prompt文本文件 | 历史版本 |
| `Prompt-template/` | 目录 | Prompt模板文件 | 历史版本 |
| `README_使用说明.md` | 文档 | 早期的使用说明文档 | 历史版本 |

## 🔄 与新版本的关系

### 功能演进
1. **早期版本**（legacy_files中的文件）：
   - 基础的Excel处理功能
   - 简单的Prompt生成
   - 单一功能的脚本

2. **新版本**（prompt_parser目录）：
   - 完整的Prompt解析功能
   - Output内容解析
   - SessionID科学计数法修复
   - 模块化设计
   - 完善的验证和演示工具

### 数据流程对比
- **历史版本**：Excel → 简单处理 → 输出
- **新版本**：Excel → 双重解析（Prompt+Output） → 结构化输出 → 多重验证

## ⚠️ 重要说明

1. **不建议使用**：这些历史文件仅作为参考保留，不建议在生产环境中使用
2. **功能重复**：新的prompt_parser已经包含了这些文件的所有功能，并且更加完善
3. **兼容性**：历史文件可能与当前数据格式不完全兼容
4. **维护状态**：这些文件不再维护更新

## 🗂️ 推荐使用

请使用 `../prompt_parser/` 目录中的新版本工具：
- 功能更完整
- 错误处理更完善
- 文档更详细
- 持续维护更新

## 🔍 如需查看历史版本

如果需要了解功能的演进过程或查看早期实现方式，可以参考这些历史文件。但对于实际使用，强烈建议使用新版本的prompt_parser工具。

---

**整理时间**：2025-08-12  
**整理原因**：项目重构，将历史文件与新版本分离  
**建议**：使用 `../prompt_parser/` 中的新版本工具
