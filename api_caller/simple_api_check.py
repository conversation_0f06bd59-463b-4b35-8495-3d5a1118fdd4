#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单检查prompt_parser.py是否包含真正的API调用代码
"""

import os

def simple_api_check():
    """简单检查API调用"""
    
    script_path = "/Users/<USER>/Cursor-Project/Temp-excel/prompt_parser/prompt_parser.py"
    
    print("🔍 检查prompt_parser.py是否包含API调用代码")
    print("=" * 60)
    
    # 真正的API调用关键词
    real_api_keywords = [
        'requests.',
        'http://',
        'https://',
        'urllib',
        'aiohttp',
        'httpx',
        'curl',
        'Authorization',
        'Bearer',
        'Content-Type: application/json'
    ]
    
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        found_api_calls = []
        
        for keyword in real_api_keywords:
            if keyword.lower() in content.lower():
                found_api_calls.append(keyword)
        
        if found_api_calls:
            print("⚠️  发现可能的API调用关键词:")
            for keyword in found_api_calls:
                print(f"  - {keyword}")
        else:
            print("✅ 确认：prompt_parser.py中没有发现真正的API调用代码")
        
        # 检查导入
        print("\n📦 检查导入的模块:")
        lines = content.split('\n')
        import_lines = [line.strip() for line in lines if line.strip().startswith('import ') or line.strip().startswith('from ')]
        
        network_imports = []
        for imp in import_lines:
            if any(net_lib in imp.lower() for net_lib in ['requests', 'urllib', 'http', 'aiohttp', 'httpx']):
                network_imports.append(imp)
        
        if network_imports:
            print("⚠️  发现网络相关导入:")
            for imp in network_imports:
                print(f"  - {imp}")
        else:
            print("✅ 没有发现网络相关的导入模块")
        
        # 最终结论
        print(f"\n🎯 最终结论:")
        if not found_api_calls and not network_imports:
            print("✅ prompt_parser.py是纯数据解析工具，不包含任何API调用功能")
            print("✅ 该工具仅处理Excel文件中的数据，不会连接外部服务")
        else:
            print("⚠️  可能包含API调用相关代码，需要进一步检查")
            
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")

if __name__ == "__main__":
    simple_api_check()
