#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查prompt_parser.py是否包含API调用代码
"""

import re
import os

def check_for_api_calls():
    """检查prompt_parser.py是否包含API调用相关代码"""
    
    script_path = "/Users/<USER>/Cursor-Project/Temp-excel/prompt_parser/prompt_parser.py"
    
    if not os.path.exists(script_path):
        print(f"❌ 文件不存在: {script_path}")
        return
    
    print("🔍 检查prompt_parser.py是否包含API调用代码")
    print("=" * 60)
    
    # 定义可能的API调用相关关键词
    api_keywords = [
        r'requests\.',
        r'http[s]?://',
        r'\.post\(',
        r'\.get\(',
        r'\.put\(',
        r'\.delete\(',
        r'curl',
        r'urllib',
        r'aiohttp',
        r'httpx',
        r'fetch\(',
        r'Authorization',
        r'Bearer',
        r'Content-Type.*application/json'
    ]
    
    # 排除的正常关键词（这些不是API调用）
    exclude_patterns = [
        r'call_record',  # 通话记录变量名
        r'CallActivity', # JSON字段名
    ]
    
    found_issues = []
    
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line_num, line in enumerate(lines, 1):
            line_stripped = line.strip()
            
            # 跳过注释行
            if line_stripped.startswith('#'):
                continue
            
            # 检查API相关关键词
            for pattern in api_keywords:
                if re.search(pattern, line, re.IGNORECASE):
                    # 检查是否是排除的模式
                    is_excluded = False
                    for exclude_pattern in exclude_patterns:
                        if re.search(exclude_pattern, line, re.IGNORECASE):
                            is_excluded = True
                            break
                    
                    if not is_excluded:
                        found_issues.append({
                            'line_num': line_num,
                            'line': line_stripped,
                            'pattern': pattern
                        })
        
        # 报告结果
        if found_issues:
            print("⚠️  发现可能的API调用代码:")
            for issue in found_issues:
                print(f"  第{issue['line_num']}行: {issue['line']}")
                print(f"  匹配模式: {issue['pattern']}")
                print()
        else:
            print("✅ 确认：prompt_parser.py中没有发现API调用代码")
            print("✅ 该脚本仅进行数据解析，不会调用任何外部API")
        
        # 检查导入的模块
        print("\n📦 检查导入的模块:")
        import_lines = [line.strip() for line in lines if line.strip().startswith('import ') or line.strip().startswith('from ')]
        
        api_related_imports = []
        for imp in import_lines:
            if any(keyword in imp.lower() for keyword in ['requests', 'urllib', 'http', 'aiohttp', 'httpx']):
                api_related_imports.append(imp)
            else:
                print(f"  ✅ {imp}")
        
        if api_related_imports:
            print("\n⚠️  发现可能的API相关导入:")
            for imp in api_related_imports:
                print(f"  ❌ {imp}")
        else:
            print("  ✅ 所有导入的模块都是数据处理相关，无API调用模块")
            
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")

if __name__ == "__main__":
    check_for_api_calls()
