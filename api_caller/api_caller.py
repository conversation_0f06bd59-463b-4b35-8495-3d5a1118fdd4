#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API调用器
用于调用Aida接口，处理Excel中的Prompt数据，并将返回结果添加到新的Excel文件中

功能：
1. 读取处理后的Excel文件
2. 调用Aida API接口
3. 解析返回的JSON数据
4. 将结果添加到新的Excel列中
5. 支持并发调用和试跑模式

作者：AI Assistant
创建时间：2025-08-12
"""

import json
import os
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime

import pandas as pd
import requests


class ApiCaller:
    def __init__(self):
        self.api_url = "https://aida.vip.sankuai.com/v1/chat-messages"
        self.headers = {
            'Authorization': 'Bearer app-IYHtLej4teaPvCm35VKUhjDG',
            'Content-Type': 'application/json'
        }
        self.model_name = ""
        self.concurrent_limit = 1
        self.test_mode = False
        self.test_count = 3
        
    def get_user_inputs(self):
        """获取用户输入的参数"""
        print("🔧 API调用器配置")
        print("=" * 50)
        
        # 获取模型名称
        while True:
            model_name = input("请输入模型名称 (例如: LongCat-Large-test): ").strip()
            if model_name:
                self.model_name = model_name
                break
            print("❌ 模型名称不能为空，请重新输入")
        
        # 询问运行模式
        while True:
            mode = input("请选择运行模式 [1: 试跑前3条, 2: 全量运行]: ").strip()
            if mode == "1":
                self.test_mode = True
                print(f"✅ 选择试跑模式，将处理前 {self.test_count} 条数据")
                break
            elif mode == "2":
                self.test_mode = False
                print("✅ 选择全量运行模式")
                break
            else:
                print("❌ 请输入 1 或 2")
        
        # 询问并发数
        while True:
            try:
                concurrent = input("请输入并发数 [1-5, 默认1]: ").strip()
                if not concurrent:
                    concurrent = "1"
                concurrent_num = int(concurrent)
                if 1 <= concurrent_num <= 5:
                    self.concurrent_limit = concurrent_num
                    print(f"✅ 设置并发数为 {self.concurrent_limit}")
                    break
                else:
                    print("❌ 并发数必须在1-5之间")
            except ValueError:
                print("❌ 请输入有效的数字")
    
    def call_api(self, prompt, session_id=""):
        """调用API接口"""
        payload = {
            "inputs": {
                "prompt": prompt,
                "modelName": self.model_name
            },
            "response_mode": "blocking",
            "user": f"api-caller-{session_id}"
        }
        
        try:
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ API调用失败，状态码: {response.status_code}")
                return None
                
        except requests.exceptions.Timeout:
            print("❌ API调用超时")
            return None
        except requests.exceptions.RequestException as e:
            print(f"❌ API调用异常: {e}")
            return None
    
    def parse_api_response(self, response_data):
        """解析API返回的数据"""
        if not response_data or 'answer' not in response_data:
            return {
                'thought': '',
                'response': '',
                'relevant_business_knowledge': '',
                'response_solution': '',
                'dialogue_agree_solution': ''
            }

        try:
            # 解析answer字段中的JSON
            answer_str = response_data['answer']
            answer_data = json.loads(answer_str)

            # 检查是否有data字段且data是JSON字符串
            if 'data' in answer_data:
                data_content = answer_data['data']

                # 如果data是字符串，尝试解析为JSON
                if isinstance(data_content, str):
                    try:
                        data_json = json.loads(data_content)
                        return {
                            'thought': data_json.get('Thought', ''),
                            'response': data_json.get('Response', ''),
                            'relevant_business_knowledge': str(data_json.get('RelevantBusinessKnowledgeNumber', [])),
                            'response_solution': data_json.get('ResponseSolution', ''),
                            'dialogue_agree_solution': data_json.get('DialogueAgreeSolution', '')
                        }
                    except json.JSONDecodeError:
                        # 如果data不是JSON，直接作为response使用
                        return {
                            'thought': '',
                            'response': data_content,
                            'relevant_business_knowledge': '',
                            'response_solution': '',
                            'dialogue_agree_solution': ''
                        }
                else:
                    # 如果data已经是字典
                    return {
                        'thought': data_content.get('Thought', ''),
                        'response': data_content.get('Response', ''),
                        'relevant_business_knowledge': str(data_content.get('RelevantBusinessKnowledgeNumber', [])),
                        'response_solution': data_content.get('ResponseSolution', ''),
                        'dialogue_agree_solution': data_content.get('DialogueAgreeSolution', '')
                    }
            else:
                # 如果没有data字段，检查是否直接包含我们需要的字段
                return {
                    'thought': answer_data.get('Thought', ''),
                    'response': answer_data.get('Response', answer_data.get('response', '')),
                    'relevant_business_knowledge': str(answer_data.get('RelevantBusinessKnowledgeNumber', [])),
                    'response_solution': answer_data.get('ResponseSolution', answer_data.get('solutions', '')),
                    'dialogue_agree_solution': answer_data.get('DialogueAgreeSolution', answer_data.get('agreeSolutions', ''))
                }

        except (json.JSONDecodeError, KeyError) as e:
            print(f"❌ 解析API响应失败: {e}")
            print(f"原始answer内容: {response_data.get('answer', '')[:200]}...")
            return {
                'thought': '',
                'response': '',
                'relevant_business_knowledge': '',
                'response_solution': '',
                'dialogue_agree_solution': ''
            }
    
    def process_batch(self, batch_data):
        """处理一批数据"""
        results = []
        
        with ThreadPoolExecutor(max_workers=self.concurrent_limit) as executor:
            # 提交所有任务
            future_to_index = {}
            for i, (index, row) in enumerate(batch_data):
                prompt = row.get('处理后的Prompt', '')
                session_id = row.get('session_id', f'batch_{i}')
                
                future = executor.submit(self.call_api, prompt, session_id)
                future_to_index[future] = (index, row)
            
            # 收集结果
            for future in as_completed(future_to_index):
                index, row = future_to_index[future]
                try:
                    api_response = future.result()
                    parsed_data = self.parse_api_response(api_response)
                    results.append((index, parsed_data))
                    print(f"✅ 完成第 {index + 1} 条数据的API调用")
                except Exception as e:
                    print(f"❌ 第 {index + 1} 条数据处理失败: {e}")
                    # 添加空结果
                    empty_result = {
                        'thought': '',
                        'response': '',
                        'relevant_business_knowledge': '',
                        'response_solution': '',
                        'dialogue_agree_solution': ''
                    }
                    results.append((index, empty_result))
        
        return results

    def process_excel(self, input_file, output_file=None):
        """处理Excel文件"""
        try:
            # 读取Excel文件
            print(f"📖 正在读取Excel文件: {input_file}")
            df = pd.read_excel(input_file, dtype={'session_id': str})

            # 检查必要的列
            if '处理后的Prompt' not in df.columns:
                print("❌ Excel文件中未找到'处理后的Prompt'列")
                return None

            print(f"📊 文件包含 {len(df)} 行数据")

            # 确定处理的数据范围
            if self.test_mode:
                process_df = df.head(self.test_count).copy()
                print(f"🧪 试跑模式：处理前 {len(process_df)} 条数据")
            else:
                process_df = df.copy()
                print(f"🚀 全量模式：处理全部 {len(process_df)} 条数据")

            # 初始化新列
            process_df['API模型思考'] = ''
            process_df['API模型回复'] = ''
            process_df['API模型引用的约束'] = ''
            process_df['API模型输出方案'] = ''
            process_df['API模型共识的方案'] = ''

            # 分批处理数据
            batch_size = self.concurrent_limit
            total_batches = (len(process_df) + batch_size - 1) // batch_size

            print(f"🔄 开始API调用，并发数: {self.concurrent_limit}，共 {total_batches} 批")

            start_time = time.time()
            processed_count = 0

            for batch_num in range(total_batches):
                start_idx = batch_num * batch_size
                end_idx = min(start_idx + batch_size, len(process_df))

                print(f"\n📦 处理第 {batch_num + 1}/{total_batches} 批 (第{start_idx + 1}-{end_idx}条)")

                # 准备批次数据
                batch_data = []
                for i in range(start_idx, end_idx):
                    batch_data.append((i, process_df.iloc[i]))

                # 处理批次
                batch_results = self.process_batch(batch_data)

                # 更新DataFrame
                for index, result_data in batch_results:
                    process_df.at[index, 'API模型思考'] = result_data['thought']
                    process_df.at[index, 'API模型回复'] = result_data['response']
                    process_df.at[index, 'API模型引用的约束'] = result_data['relevant_business_knowledge']
                    process_df.at[index, 'API模型输出方案'] = result_data['response_solution']
                    process_df.at[index, 'API模型共识的方案'] = result_data['dialogue_agree_solution']

                processed_count += len(batch_data)
                elapsed_time = time.time() - start_time
                avg_time = elapsed_time / processed_count
                remaining_count = len(process_df) - processed_count
                estimated_remaining = avg_time * remaining_count

                print(f"⏱️  已处理: {processed_count}/{len(process_df)}, "
                      f"耗时: {elapsed_time:.1f}s, "
                      f"预计剩余: {estimated_remaining:.1f}s")

                # 短暂休息避免过于频繁的请求
                if batch_num < total_batches - 1:
                    time.sleep(0.5)

            # 生成输出文件名
            if output_file is None:
                input_basename = os.path.splitext(os.path.basename(input_file))[0]
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                mode_suffix = "试跑" if self.test_mode else "全量"
                output_file = f"{input_basename}_API调用结果_{mode_suffix}_{timestamp}.xlsx"

            # 保存结果
            print(f"\n💾 正在保存结果到: {output_file}")
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                process_df.to_excel(writer, index=False, sheet_name='API调用结果')

            total_time = time.time() - start_time
            print(f"✅ 处理完成！")
            print(f"📊 统计信息:")
            print(f"   - 总处理数量: {len(process_df)} 条")
            print(f"   - 总耗时: {total_time:.1f} 秒")
            print(f"   - 平均耗时: {total_time/len(process_df):.2f} 秒/条")
            print(f"   - 输出文件: {output_file}")

            return output_file

        except Exception as e:
            print(f"❌ 处理Excel文件时出错: {e}")
            return None

def main():
    """主函数"""
    print("🚀 API调用器")
    print("=" * 50)

    # 创建API调用器实例
    caller = ApiCaller()

    # 获取用户输入
    caller.get_user_inputs()

    # 默认输入文件
    default_input = "../prompt_parser/Aida-node_88294559_20250812_解析结果_20250812_141045.xlsx"

    # 检查文件是否存在
    if not os.path.exists(default_input):
        print(f"❌ 默认输入文件不存在: {default_input}")
        input_file = input("请输入Excel文件路径: ").strip()
        if not os.path.exists(input_file):
            print(f"❌ 文件不存在: {input_file}")
            return
    else:
        input_file = default_input
        print(f"📁 使用默认输入文件: {input_file}")

    # 处理文件
    result = caller.process_excel(input_file)

    if result:
        print(f"\n🎉 API调用完成！")
        print(f"📄 结果文件: {result}")
    else:
        print("\n❌ API调用失败！")

if __name__ == "__main__":
    main()
