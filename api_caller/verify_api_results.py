#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证API调用结果的脚本
"""

import pandas as pd
import glob
import os

def verify_api_results():
    """验证API调用结果"""
    # 查找最新的API调用结果文件
    pattern = "*API调用结果*.xlsx"
    files = glob.glob(pattern)
    
    if not files:
        print("未找到API调用结果文件")
        return
    
    # 选择最新的文件
    latest_file = max(files, key=os.path.getctime)
    print(f"验证文件: {latest_file}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(latest_file, dtype={'session_id': str})
        
        print(f"\n文件基本信息:")
        print(f"总行数: {len(df)}")
        print(f"总列数: {len(df.columns)}")
        print(f"所有列名: {list(df.columns)}")
        
        # 检查新增的API调用列
        api_columns = ['API模型思考', 'API模型回复', 'API模型引用的约束', 'API模型输出方案', 'API模型共识的方案']
        
        print(f"\nAPI调用列验证:")
        for col in api_columns:
            if col in df.columns:
                non_empty = df[df[col].str.strip() != ""][col].count()
                print(f"✅ {col}: {non_empty} 行有内容")
            else:
                print(f"❌ {col}: 列不存在")
        
        # 显示前3行的API调用结果示例
        if len(df) > 0:
            print(f"\n前{min(3, len(df))}行API调用结果示例:")
            print("="*80)
            
            for i in range(min(3, len(df))):
                print(f"\n--- 第 {i+1} 行 ---")
                print(f"Session ID: {df.iloc[i].get('session_id', 'N/A')}")
                
                for col in api_columns:
                    if col in df.columns and pd.notna(df.iloc[i][col]):
                        content = str(df.iloc[i][col])
                        # 显示前150字符
                        if len(content) > 150:
                            print(f"{col}: {content[:150]}...")
                        else:
                            print(f"{col}: {content}")
                    else:
                        print(f"{col}: (空)")
            
            print("="*80)
        
        # 对比原始模型输出和API调用结果
        original_cols = ['模型思考', '模型回复', '模型引用的约束', '模型输出方案', '模型共识的方案']
        api_cols = ['API模型思考', 'API模型回复', 'API模型引用的约束', 'API模型输出方案', 'API模型共识的方案']
        
        print(f"\n原始输出 vs API调用结果对比:")
        for orig_col, api_col in zip(original_cols, api_cols):
            if orig_col in df.columns and api_col in df.columns:
                orig_count = df[df[orig_col].str.strip() != ""][orig_col].count()
                api_count = df[df[api_col].str.strip() != ""][api_col].count()
                print(f"{orig_col}: {orig_count} 行 → {api_col}: {api_count} 行")
        
        # 检查数据质量
        print(f"\n数据质量检查:")
        for col in api_columns:
            if col in df.columns:
                total_rows = len(df)
                non_empty_rows = df[df[col].str.strip() != ""][col].count()
                empty_rows = total_rows - non_empty_rows
                success_rate = (non_empty_rows / total_rows * 100) if total_rows > 0 else 0
                print(f"{col}: {success_rate:.1f}% 成功率 ({non_empty_rows}/{total_rows})")
                
    except Exception as e:
        print(f"验证过程中出错: {e}")

if __name__ == "__main__":
    verify_api_results()
