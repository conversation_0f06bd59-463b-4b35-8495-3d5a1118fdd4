#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API测试脚本
用于测试单个API调用是否正常工作
"""

import json

import requests


def test_single_api_call():
    """测试单个API调用"""
    api_url = "https://aida.vip.sankuai.com/v1/chat-messages"
    headers = {
        'Authorization': 'Bearer app-IYHtLej4teaPvCm35VKUhjDG',
        'Content-Type': 'application/json'
    }
    
    # 测试用的简单prompt
    test_prompt = "用户询问：我的订单什么时候能到？"
    
    payload = {
        "inputs": {
            "prompt": test_prompt,
            "modelName": "LongCat-Large-test"
        },
        "response_mode": "blocking",
        "user": "test-user-123"
    }
    
    print("🧪 测试API调用")
    print("=" * 50)
    print(f"URL: {api_url}")
    print(f"测试Prompt: {test_prompt}")
    
    try:
        print("\n🔄 发送请求...")
        response = requests.post(
            api_url,
            headers=headers,
            json=payload,
            timeout=30
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("✅ API调用成功！")
            
            # 解析响应
            if 'answer' in response_data:
                print(f"\n📄 完整answer字段:")
                print(response_data['answer'])

                try:
                    answer_data = json.loads(response_data['answer'])
                    print(f"\n🎯 解析后的answer结构:")
                    for key, value in answer_data.items():
                        if isinstance(value, str) and len(value) > 100:
                            print(f"{key}: {value[:100]}...")
                        else:
                            print(f"{key}: {value}")

                    # 检查data字段
                    if 'data' in answer_data:
                        data_content = answer_data['data']
                        print(f"\n📋 data字段类型: {type(data_content)}")

                        if isinstance(data_content, str):
                            try:
                                data_json = json.loads(data_content)
                                print(f"✅ data是JSON字符串，解析后的字段:")
                                for key, value in data_json.items():
                                    print(f"  {key}: {value}")
                            except json.JSONDecodeError:
                                print(f"❌ data不是有效的JSON字符串")
                                print(f"data内容: {data_content[:200]}...")
                        else:
                            print(f"data内容: {data_content}")

                except json.JSONDecodeError as e:
                    print(f"❌ 解析answer字段失败: {e}")
                    print(f"answer原始内容: {response_data['answer']}")
            
            print(f"\n📋 完整响应结构:")
            for key in response_data.keys():
                print(f"  - {key}: {type(response_data[key])}")
                
        else:
            print(f"❌ API调用失败")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")

if __name__ == "__main__":
    test_single_api_call()
