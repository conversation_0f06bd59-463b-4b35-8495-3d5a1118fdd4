# API调用器使用说明

## 📋 功能概述

这个工具用于调用Aida API接口，处理Excel文件中的Prompt数据，并将API返回的结果添加到新的Excel文件中。

## 🎯 主要功能

1. **Excel数据读取**：读取包含"处理后的Prompt"列的Excel文件
2. **API接口调用**：调用Aida API接口获取模型响应
3. **并发处理**：支持1-5个并发请求，提高处理效率
4. **试跑模式**：支持试跑前3条数据或全量处理
5. **结果解析**：自动解析API返回的JSON数据
6. **Excel输出**：将结果保存到新的Excel文件中

## 📁 文件结构

```
api_caller/
├── api_caller.py          # 🔧 主要的API调用脚本
├── test_api.py           # 🧪 API测试脚本
└── README.md             # 📖 使用说明（本文件）
```

## 🚀 快速开始

### 基本使用

```bash
# 进入API调用器目录
cd api_caller

# 运行API调用器
python api_caller.py
```

### 交互式配置

运行脚本后，系统会依次询问：

1. **模型名称**：输入要使用的模型名称（如：LongCat-Large-test）
2. **运行模式**：选择试跑前3条数据还是全量处理
3. **并发数**：设置并发请求数量（1-5）

### 示例交互

```
🔧 API调用器配置
==================================================
请输入模型名称 (例如: LongCat-Large-test): LongCat-Large-test
请选择运行模式 [1: 试跑前3条, 2: 全量运行]: 1
请输入并发数 [1-5, 默认1]: 2
```

## 📊 输出结果

处理后的Excel文件将包含以下新增列：

| 列名 | 描述 | 来源字段 |
|------|------|----------|
| API模型思考 | 模型的思考过程 | Thought |
| API模型回复 | 模型给出的客服回复 | Response |
| API模型引用的约束 | 模型引用的业务知识编号 | RelevantBusinessKnowledgeNumber |
| API模型输出方案 | 模型输出的执行方案 | ResponseSolution |
| API模型共识的方案 | 模型与用户达成共识的方案 | DialogueAgreeSolution |

## 🔧 API接口详情

### 接口地址
```
https://aida.vip.sankuai.com/v1/chat-messages
```

### 请求格式
```json
{
    "inputs": {
        "prompt": "处理后的Prompt内容",
        "modelName": "用户指定的模型名称"
    },
    "response_mode": "blocking",
    "user": "api-caller-{session_id}"
}
```

### 响应解析
API返回的`answer`字段包含嵌套的JSON数据，脚本会自动解析其中的`data`字段来提取所需信息。

## ⚙️ 技术特点

### 并发处理
- 支持1-5个并发请求
- 使用ThreadPoolExecutor实现并发
- 自动批次处理，避免过载

### 错误处理
- 请求超时处理（30秒）
- 网络异常处理
- JSON解析错误处理
- 自动重试机制

### 性能优化
- 批次间短暂休息（0.5秒）
- 实时进度显示
- 预计剩余时间计算

## 📝 使用注意事项

1. **输入文件要求**：
   - 必须包含"处理后的Prompt"列
   - 建议使用prompt_parser处理后的Excel文件

2. **网络要求**：
   - 需要能够访问Aida API接口
   - 稳定的网络连接

3. **并发限制**：
   - 最大并发数为5
   - 建议根据网络状况调整

4. **API限制**：
   - 请求超时时间为30秒
   - 需要有效的Authorization token

## 🧪 测试功能

### API连接测试

```bash
# 测试API连接是否正常
python test_api.py
```

这个测试脚本会：
- 发送一个简单的测试请求
- 验证API响应格式
- 显示解析后的关键字段

## 🔍 故障排除

### 常见问题

1. **API调用失败**
   - 检查网络连接
   - 验证Authorization token
   - 确认API地址正确

2. **文件读取失败**
   - 确认Excel文件路径正确
   - 检查文件是否包含"处理后的Prompt"列
   - 确保文件未被其他程序占用

3. **解析错误**
   - 检查API返回格式是否符合预期
   - 查看错误日志信息

### 调试方法

```bash
# 1. 先测试API连接
python test_api.py

# 2. 使用试跑模式测试
python api_caller.py
# 选择试跑模式，并发数设为1

# 3. 检查输出文件
# 查看生成的Excel文件是否包含预期的列
```

## 📈 性能参考

- **单个请求**：平均响应时间 2-5 秒
- **并发处理**：建议并发数 2-3 个
- **处理速度**：约 10-20 条/分钟（取决于网络和并发设置）

## 🔄 版本历史

- **v1.0** (2025-08-12): 初始版本，支持基本的API调用和Excel处理功能

---

**最后更新**：2025-08-12
