# Temp-excel 项目总览

## 📋 项目概述

这是一个Excel数据处理项目，主要用于解析和处理Prompt相关的数据。项目经过重构，现在包含了完整的Prompt解析器和历史文件归档。

## 📁 项目结构

```
Temp-excel/
├── prompt_parser/           # 🔧 主要工具目录（推荐使用）
│   ├── prompt_parser.py     # 主解析脚本
│   ├── README.md           # 详细使用说明
│   ├── 各种验证和演示工具   # 完整的工具集
│   └── 解析结果文件        # 处理后的Excel文件
├── legacy_files/           # 📦 历史文件归档
│   ├── 早期版本脚本        # 不再维护的历史版本
│   ├── 历史数据文件        # 早期的数据文件
│   └── README_历史文件说明.md # 历史文件详细说明
└── README.md              # 项目总览（本文件）
```

## 🎯 主要功能

### prompt_parser/ - 数据解析工具
- ✅ **Prompt内容完整解析**：符号替换、内容提取、结构化输出
- ✅ **Output内容解析**：模型思考、回复、方案等字段提取
- ✅ **SessionID修复**：解决科学计数法显示问题
- ✅ **文件管理优化**：自动化文件处理和保存
- ✅ **完整验证工具**：多重验证确保数据准确性
- ⚠️ **仅进行解析**：不会自动调用任何API接口

### api_caller/ - API调用工具
- ✅ **API接口调用**：调用Aida API获取新的模型响应
- ✅ **并发处理**：支持1-5个并发请求
- ✅ **交互配置**：用户自定义模型名称、运行模式、并发数
- ✅ **试跑模式**：支持试跑前3条或全量处理
- ✅ **结果整合**：将API结果添加到Excel的新列中

## 🚀 快速开始

### 数据解析（推荐第一步）

```bash
# 进入解析工具目录
cd prompt_parser

# 运行解析器（仅进行数据解析，不调用API）
python prompt_parser.py your_excel_file.xlsx

# 验证解析结果
python verify_output_parsing.py
```

### API调用（可选第二步）

```bash
# 进入API调用工具目录
cd api_caller

# 使用解析后的Excel文件调用API获取新响应
python api_caller.py ../prompt_parser/解析结果文件.xlsx

# 按提示配置：模型名称、运行模式、并发数
```

### 详细使用说明

请查看 `prompt_parser/README.md` 获取完整的使用说明和功能介绍。

## 📊 功能特点

- **双重解析**：同时解析Prompt和Output内容
- **自动修复**：解决SessionID科学计数法问题
- **结构化输出**：将非结构化数据转换为13个结构化列
- **完整验证**：多重验证工具确保数据准确性
- **易于使用**：一键运行，自动处理所有步骤

## 📈 处理能力

- **数据量**：支持大批量Excel文件处理
- **成功率**：98.9%的解析成功率
- **输出格式**：13列结构化数据（3原始+5Prompt+5Output）
- **错误处理**：完善的异常处理和错误恢复

## 📂 目录说明

### prompt_parser/ - 主要工具目录
包含完整的Prompt解析器和所有相关工具，这是推荐使用的版本。

### legacy_files/ - 历史文件归档
包含项目早期版本的脚本和数据文件，仅作为参考保留，不建议使用。

## ⚠️ 重要提醒

1. **推荐使用**：请使用 `prompt_parser/` 目录中的工具
2. **历史文件**：`legacy_files/` 中的文件仅作参考，不建议使用
3. **完整文档**：详细使用说明请查看 `prompt_parser/README.md`

## 🔄 项目演进

- **早期版本**：基础Excel处理功能（已归档至legacy_files）
- **当前版本**：完整的双重解析功能（prompt_parser目录）
- **未来计划**：持续优化和功能扩展

---

**项目整理时间**：2025-08-12
**当前版本**：v2.0（支持Prompt+Output双重解析）
**推荐使用**：`cd prompt_parser && python prompt_parser.py`
