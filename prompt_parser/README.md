# Prompt解析器使用说明

## 📋 功能概述

这个工具专门用于解析Excel文件中的Prompt和Output内容，将其重新组织为结构化的列，便于后续分析和处理。

⚠️ **重要说明**：本工具仅进行数据解析，**不会自动调用任何API接口**。如需调用API获取新的模型响应，请使用单独的 `api_caller` 工具。

## 🎯 主要功能

### Prompt内容解析
1. **符号替换**：将Prompt中的`¥¥`替换成`\n`（换行符）
2. **业务知识提取**：提取`可能用到的业务知识如下：`和`可能用到的方案列表如下：`之间的内容
3. **系统信号提取**：提取`系统信号如下：`和`客服和商家或者骑手的最近一次通话记录如下:`之间的内容
4. **外呼通话记录提取**：提取`客服和商家或者骑手的最近一次通话记录如下:`和`对话历史如下：`之间的内容
5. **历史上下文提取**：提取`对话历史如下：`之后的所有内容

### Output内容解析（如果Excel包含output列）
6. **模型思考提取**：从output的JSON中提取`Thought`字段
7. **模型回复提取**：从output的JSON中提取`Response`字段
8. **模型引用约束提取**：从output的JSON中提取`RelevantBusinessKnowledgeNumber`字段
9. **模型输出方案提取**：从output的JSON中提取`ResponseSolution`字段
10. **模型共识方案提取**：从output的JSON中提取`DialogueAgreeSolution`字段

## 📁 文件结构

```
prompt_parser/
├── prompt_parser.py          # 主解析脚本
├── README.md                 # 使用说明（本文件）
├── analyze_excel_structure.py # Excel结构分析工具
└── view_full_prompt.py       # Prompt内容查看工具
```

## 🚀 快速开始

### 基本用法

```bash
# 使用默认输入文件
python prompt_parser.py

# 指定输入文件
python prompt_parser.py /path/to/your/excel_file.xlsx

# 指定输入和输出文件
python prompt_parser.py input.xlsx output.xlsx
```

### 示例

```bash
# 处理默认文件
cd prompt_parser
python prompt_parser.py

# 处理指定文件
python prompt_parser.py ../Aida-node_88294559_20250812.xlsx

# 指定输出文件名
python prompt_parser.py ../input.xlsx ../output_parsed.xlsx
```

## 📊 输出结果

处理后的Excel文件将包含以下新增列：

### Prompt解析列（总是包含）
| 列名 | 描述 | 示例内容 |
|------|------|----------|
| 业务知识 | 从Prompt中提取的业务知识规则 | "5.当用户反馈"无骑手接单"时..." |
| 系统信号 | 从Prompt中提取的系统状态信息 | "{现在时间:20250811 21:19}..." |
| 外呼通话记录 | 客服与商家/骑手的通话记录 | "空" 或具体通话内容 |
| 历史上下文 | 用户与客服的对话历史 | "用户:无骑手接单\n客服:..." |
| 处理后的Prompt | 替换¥¥后的完整Prompt | 完整的处理后Prompt文本 |

### Output解析列（如果Excel包含output列）
| 列名 | 描述 | 示例内容 |
|------|------|----------|
| 模型思考 | 模型的思考过程 | "用户反馈订单超时，系统信号显示..." |
| 模型回复 | 模型给出的客服回复 | "非常抱歉给您带来不便，我们将为您..." |
| 模型引用的约束 | 模型引用的业务知识编号 | "['27']" 或 "['0']" |
| 模型输出方案 | 模型输出的执行方案 | "操作赔付-20元-红包" |
| 模型共识的方案 | 模型与用户达成共识的方案 | "操作赔付-20元-红包" 或 "无" |

## ⚠️ 重要提醒

- **本工具仅进行数据解析**：不会调用任何外部API接口
- **如需API调用**：请使用 `../api_caller/` 目录中的API调用工具
- **工具分离设计**：解析和API调用分开，便于灵活使用

## 🔧 辅助工具

### 1. Excel结构分析工具

```bash
python analyze_excel_structure.py [excel_file]
```

用于分析Excel文件的结构，查看列名和Prompt内容示例。

### 2. Prompt内容查看工具

```bash
python view_full_prompt.py
```

用于查看完整的Prompt内容，便于理解结构。

## ⚙️ 技术细节

### 依赖库

- `pandas`: Excel文件读写
- `openpyxl`: Excel文件处理引擎
- `re`: 正则表达式（预留）
- `os`, `sys`: 系统操作
- `datetime`: 时间戳生成

### 安装依赖

```bash
pip install pandas openpyxl
```

### 解析逻辑

1. **文本预处理**：将`¥¥`替换为`\n`
2. **标记识别**：通过关键字标记定位各个部分
3. **内容提取**：使用字符串查找和切片提取内容
4. **结果组织**：将提取的内容组织到新的Excel列中

## 📝 注意事项

1. **输入文件要求**：
   - 必须是Excel格式（.xlsx）
   - 必须包含名为`prompt`的列（不区分大小写）
   - Prompt内容应包含标准的分隔标记

2. **输出文件**：
   - 如果不指定输出文件名，会自动生成带时间戳的文件名
   - 输出文件会保留原有的所有列，并新增解析后的列

3. **性能考虑**：
   - 大文件处理时会显示进度信息
   - 每10行显示一次处理进度

4. **错误处理**：
   - 自动跳过空的Prompt行
   - 对于格式不标准的Prompt，相应字段会为空

## 🔍 故障排除

### 常见问题

1. **找不到prompt列**
   - 检查Excel文件是否包含prompt相关的列名
   - 列名可以是`prompt`、`Prompt`等变体

2. **提取内容为空**
   - 检查Prompt中是否包含标准的分隔标记
   - 使用`view_full_prompt.py`查看原始内容

3. **文件读取失败**
   - 确保文件路径正确
   - 确保文件不被其他程序占用
   - 检查文件格式是否为Excel

### 调试方法

```bash
# 查看Excel结构
python analyze_excel_structure.py your_file.xlsx

# 查看具体Prompt内容
python view_full_prompt.py
```

## 📈 使用场景

- **数据分析**：将非结构化的Prompt内容转换为结构化数据
- **内容审核**：分别查看业务知识、系统信号等不同部分
- **模板优化**：分析不同类型内容的分布和特点
- **质量检查**：检查Prompt内容的完整性和规范性

## 🔄 版本历史

- **v1.0** (2025-08-12): 初始版本，支持基本的Prompt解析功能

## 📞 技术支持

如有问题或建议，请联系开发团队。

---

*最后更新：2025-08-12*
