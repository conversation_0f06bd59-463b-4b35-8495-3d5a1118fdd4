#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prompt解析器
用于解析Excel文件中的Prompt内容，并重新组织为结构化的列

功能：
1. 将Prompt中的'¥¥'替换成'\n'
2. 提取业务知识列：'可能用到的业务知识如下：'和'可能用到的方案列表如下：'之间的内容
3. 提取系统信号列：'系统信号如下：'和'客服和商家或者骑手的最近一次通话记录如下:'之间的内容
4. 提取外呼通话记录列：'客服和商家或者骑手的最近一次通话记录如下:'和'对话历史如下：'之间的内容
5. 提取历史上下文：'对话历史如下：'之后的内容

作者：AI Assistant
创建时间：2025-08-12
"""

import json
import os
import shutil
import sys
from datetime import datetime

import pandas as pd


class PromptParser:
    def __init__(self):
        self.business_knowledge_start = "可能用到的业务知识如下："
        self.business_knowledge_end = "可能用到的方案列表如下："
        self.system_signal_start = "系统信号如下："
        self.system_signal_end = "客服和商家或者骑手的最近一次通话记录如下:"
        self.call_record_start = "客服和商家或者骑手的最近一次通话记录如下:"
        self.call_record_end = "对话历史如下："
        self.dialogue_history_start = "对话历史如下："
    
    def replace_symbols(self, text):
        """将¥¥替换为\n"""
        if pd.isna(text):
            return ""
        return str(text).replace('¥¥', '\n')

    def parse_output_json(self, output_text):
        """解析output列中的JSON数据"""
        if pd.isna(output_text):
            return {
                'thought': "",
                'response': "",
                'relevant_business_knowledge': "",
                'response_solution': "",
                'dialogue_agree_solution': ""
            }

        try:
            # 解析外层JSON
            outer_json = json.loads(str(output_text))

            # 获取嵌套的JSON字符串
            nested_content = outer_json.get('CallActivity_1y4djge__complete_content_', '{}')

            # 解析嵌套的JSON
            if isinstance(nested_content, str):
                inner_json = json.loads(nested_content)
            else:
                inner_json = nested_content

            # 提取所需字段
            return {
                'thought': inner_json.get('Thought', ''),
                'response': inner_json.get('Response', ''),
                'relevant_business_knowledge': str(inner_json.get('RelevantBusinessKnowledgeNumber', [])),
                'response_solution': inner_json.get('ResponseSolution', ''),
                'dialogue_agree_solution': inner_json.get('DialogueAgreeSolution', '')
            }

        except (json.JSONDecodeError, KeyError, TypeError) as e:
            print(f"解析output JSON时出错: {e}")
            return {
                'thought': "",
                'response': "",
                'relevant_business_knowledge': "",
                'response_solution': "",
                'dialogue_agree_solution': ""
            }
    
    def extract_section(self, text, start_marker, end_marker=None):
        """提取指定标记之间的内容"""
        if pd.isna(text):
            return ""
        
        text = str(text)
        start_pos = text.find(start_marker)
        if start_pos == -1:
            return ""
        
        start_pos += len(start_marker)
        
        if end_marker:
            end_pos = text.find(end_marker, start_pos)
            if end_pos == -1:
                return text[start_pos:].strip()
            return text[start_pos:end_pos].strip()
        else:
            return text[start_pos:].strip()
    
    def parse_prompt(self, prompt_text):
        """解析单个Prompt文本"""
        if pd.isna(prompt_text):
            return {
                'processed_prompt': "",
                'business_knowledge': "",
                'system_signal': "",
                'call_record': "",
                'dialogue_history': ""
            }
        
        # 1. 替换¥¥为\n
        processed_prompt = self.replace_symbols(prompt_text)
        
        # 2. 提取业务知识
        business_knowledge = self.extract_section(
            prompt_text, 
            self.business_knowledge_start, 
            self.business_knowledge_end
        )
        business_knowledge = self.replace_symbols(business_knowledge)
        
        # 3. 提取系统信号
        system_signal = self.extract_section(
            prompt_text,
            self.system_signal_start,
            self.system_signal_end
        )
        system_signal = self.replace_symbols(system_signal)
        
        # 4. 提取外呼通话记录
        call_record = self.extract_section(
            prompt_text,
            self.call_record_start,
            self.call_record_end
        )
        call_record = self.replace_symbols(call_record)
        
        # 5. 提取对话历史
        dialogue_history = self.extract_section(
            prompt_text,
            self.dialogue_history_start
        )
        dialogue_history = self.replace_symbols(dialogue_history)
        
        return {
            'processed_prompt': processed_prompt,
            'business_knowledge': business_knowledge,
            'system_signal': system_signal,
            'call_record': call_record,
            'dialogue_history': dialogue_history
        }
    
    def process_excel(self, input_path, output_path=None):
        """处理Excel文件"""
        try:
            # 读取Excel文件，防止科学计数法
            print(f"正在读取Excel文件: {input_path}")
            df = pd.read_excel(input_path, dtype={'session_id': str})

            # 查找prompt列
            prompt_columns = [col for col in df.columns if 'prompt' in col.lower()]
            if not prompt_columns:
                raise ValueError("未找到prompt列")

            prompt_col = prompt_columns[0]
            print(f"找到prompt列: {prompt_col}")
            print(f"总共 {len(df)} 行数据")

            # 确保session_id列为字符串格式，避免科学计数法
            if 'session_id' in df.columns:
                df['session_id'] = df['session_id'].astype(str)
                print("已修复session_id的科学计数法问题")
            
            # 创建新的列
            df['业务知识'] = ""
            df['系统信号'] = ""
            df['外呼通话记录'] = ""
            df['历史上下文'] = ""
            df['处理后的Prompt'] = ""

            # 检查是否有output列，如果有则添加相应的解析列
            has_output = 'output' in df.columns
            if has_output:
                df['模型思考'] = ""
                df['模型回复'] = ""
                df['模型引用的约束'] = ""
                df['模型输出方案'] = ""
                df['模型共识的方案'] = ""
                print("检测到output列，将同时解析output内容")
            
            # 处理每一行
            processed_count = 0
            for index, row in df.iterrows():
                # 处理prompt列
                if pd.notna(row[prompt_col]):
                    parsed_data = self.parse_prompt(row[prompt_col])

                    df.at[index, '业务知识'] = parsed_data['business_knowledge']
                    df.at[index, '系统信号'] = parsed_data['system_signal']
                    df.at[index, '外呼通话记录'] = parsed_data['call_record']
                    df.at[index, '历史上下文'] = parsed_data['dialogue_history']
                    df.at[index, '处理后的Prompt'] = parsed_data['processed_prompt']

                # 处理output列（如果存在）
                if has_output and pd.notna(row['output']):
                    output_data = self.parse_output_json(row['output'])

                    df.at[index, '模型思考'] = output_data['thought']
                    df.at[index, '模型回复'] = output_data['response']
                    df.at[index, '模型引用的约束'] = output_data['relevant_business_knowledge']
                    df.at[index, '模型输出方案'] = output_data['response_solution']
                    df.at[index, '模型共识的方案'] = output_data['dialogue_agree_solution']

                processed_count += 1
                if processed_count % 10 == 0:
                    print(f"已处理 {processed_count} 行...")
            
            print(f"处理完成，共处理 {processed_count} 行数据")
            
            # 生成输出文件名，确保保存在当前文件夹下
            if output_path is None:
                input_filename = os.path.basename(input_path)
                base_name = os.path.splitext(input_filename)[0]
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = f"{base_name}_解析结果_{timestamp}.xlsx"

            # 保存结果
            print(f"正在保存结果到: {output_path}")
            # 使用engine='openpyxl'确保正确保存，避免科学计数法
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='解析结果')
            print("保存完成！")
            
            # 显示统计信息
            self.show_statistics(df)
            
            return output_path
            
        except Exception as e:
            print(f"处理Excel文件时出错: {e}")
            return None
    
    def show_statistics(self, df):
        """显示统计信息"""
        print("\n" + "="*50)
        print("处理结果统计:")
        print(f"总行数: {len(df)}")

        # Prompt解析统计
        print("\nPrompt解析结果:")
        for col in ['业务知识', '系统信号', '外呼通话记录', '历史上下文']:
            if col in df.columns:
                non_empty = df[df[col].str.strip() != ""][col].count()
                print(f"  {col}: {non_empty} 行有内容")

        # Output解析统计
        output_cols = ['模型思考', '模型回复', '模型引用的约束', '模型输出方案', '模型共识的方案']
        if any(col in df.columns for col in output_cols):
            print("\nOutput解析结果:")
            for col in output_cols:
                if col in df.columns:
                    non_empty = df[df[col].str.strip() != ""][col].count()
                    print(f"  {col}: {non_empty} 行有内容")

        print("="*50)

def main():
    """主函数"""
    parser = PromptParser()

    # 默认输入文件
    default_input = "../Aida-node_88294559_20250812.xlsx"

    # 从命令行参数获取输入文件
    input_file = sys.argv[1] if len(sys.argv) > 1 else default_input
    output_file = sys.argv[2] if len(sys.argv) > 2 else None

    if not os.path.exists(input_file):
        print(f"错误：文件不存在 - {input_file}")
        return

    # 如果输入文件不在当前文件夹，复制到当前文件夹
    local_input_file = input_file
    if not os.path.dirname(input_file) == "." and not os.path.dirname(input_file) == "":
        filename = os.path.basename(input_file)
        local_input_file = filename
        if not os.path.exists(local_input_file):
            print(f"复制输入文件到当前文件夹: {filename}")
            shutil.copy2(input_file, local_input_file)
        else:
            print(f"使用已存在的本地文件: {filename}")

    print("Prompt解析器")
    print("="*50)
    print(f"输入文件: {local_input_file}")

    result = parser.process_excel(local_input_file, output_file)
    
    if result:
        print(f"\n✅ 处理成功！")
        print(f"输出文件: {result}")
    else:
        print("\n❌ 处理失败！")

if __name__ == "__main__":
    main()
