#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证Output解析结果的脚本
检查新增的模型相关列是否正确解析
"""

import pandas as pd
import glob
import os

def verify_output_parsing():
    """验证Output解析结果"""
    # 查找最新的解析结果文件
    pattern = "*解析结果*.xlsx"
    files = glob.glob(pattern)
    
    if not files:
        print("未找到解析结果文件")
        return
    
    # 选择最新的文件
    latest_file = max(files, key=os.path.getctime)
    print(f"验证文件: {latest_file}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(latest_file, dtype={'session_id': str})
        
        print(f"\n文件基本信息:")
        print(f"总行数: {len(df)}")
        print(f"总列数: {len(df.columns)}")
        print(f"所有列名: {list(df.columns)}")
        
        # 检查新增的output解析列
        output_columns = ['模型思考', '模型回复', '模型引用的约束', '模型输出方案', '模型共识的方案']
        
        print(f"\nOutput解析列验证:")
        for col in output_columns:
            if col in df.columns:
                non_empty = df[df[col].str.strip() != ""][col].count()
                print(f"✅ {col}: {non_empty} 行有内容")
            else:
                print(f"❌ {col}: 列不存在")
        
        # 显示前3行的解析结果示例
        if len(df) > 0:
            print(f"\n前3行Output解析结果示例:")
            print("="*80)
            
            for i in range(min(3, len(df))):
                print(f"\n--- 第 {i+1} 行 ---")
                for col in output_columns:
                    if col in df.columns and pd.notna(df.iloc[i][col]):
                        content = str(df.iloc[i][col])
                        # 显示前100字符
                        if len(content) > 100:
                            print(f"{col}: {content[:100]}...")
                        else:
                            print(f"{col}: {content}")
                    else:
                        print(f"{col}: (空)")
            
            print("="*80)
        
        # 检查数据完整性
        print(f"\n数据完整性检查:")
        
        # 检查原始列是否存在
        original_cols = ['session_id', 'output', 'prompt']
        for col in original_cols:
            if col in df.columns:
                print(f"✅ 原始列 {col}: 存在")
            else:
                print(f"❌ 原始列 {col}: 缺失")
        
        # 检查Prompt解析列
        prompt_cols = ['业务知识', '系统信号', '外呼通话记录', '历史上下文', '处理后的Prompt']
        for col in prompt_cols:
            if col in df.columns:
                print(f"✅ Prompt解析列 {col}: 存在")
            else:
                print(f"❌ Prompt解析列 {col}: 缺失")
        
        # 统计各列的数据分布
        print(f"\n数据分布统计:")
        for col in output_columns:
            if col in df.columns:
                total_rows = len(df)
                non_empty_rows = df[df[col].str.strip() != ""][col].count()
                empty_rows = total_rows - non_empty_rows
                print(f"{col}: {non_empty_rows} 有内容, {empty_rows} 为空 ({non_empty_rows/total_rows*100:.1f}%)")
        
        # 检查模型引用的约束格式
        if '模型引用的约束' in df.columns:
            print(f"\n模型引用的约束格式检查:")
            constraint_values = df['模型引用的约束'].dropna().unique()
            print(f"不同的约束值: {len(constraint_values)} 种")
            for i, value in enumerate(constraint_values[:10]):  # 显示前10种
                print(f"  {i+1}: {value}")
            if len(constraint_values) > 10:
                print(f"  ... 还有 {len(constraint_values) - 10} 种")
                
    except Exception as e:
        print(f"验证过程中出错: {e}")

if __name__ == "__main__":
    verify_output_parsing()
