#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prompt解析器演示脚本
展示完整的使用流程和验证过程
"""

import os
import subprocess
import time


def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"🔧 {description}")
    print(f"命令: {command}")
    print('='*60)
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 执行成功")
            if result.stdout:
                print(result.stdout)
        else:
            print("❌ 执行失败")
            if result.stderr:
                print(result.stderr)
    except Exception as e:
        print(f"❌ 执行出错: {e}")
    
    time.sleep(1)

def main():
    """主演示流程"""
    print("🎉 Prompt解析器完整演示")
    print("=" * 80)
    print("本演示将展示完整的使用流程，包括：")
    print("1. 解析Excel文件（Prompt + Output双重解析）")
    print("2. 验证SessionID格式")
    print("3. 验证Prompt解析结果")
    print("4. 验证Output解析结果（新增功能）")
    print("5. 显示项目结构")
    
    # 1. 运行主解析脚本
    run_command(
        "python prompt_parser.py",
        "步骤1: 解析Excel文件（Prompt + Output双重解析）"
    )

    # 2. 验证SessionID格式
    run_command(
        "python verify_session_id.py",
        "步骤2: 验证SessionID格式（检查科学计数法修复）"
    )

    # 3. 验证Prompt解析结果
    run_command(
        "python verify_results.py",
        "步骤3: 验证Prompt解析结果的完整性和正确性"
    )

    # 4. 验证Output解析结果
    run_command(
        "python verify_output_parsing.py",
        "步骤4: 验证Output解析结果（新增功能验证）"
    )
    
    # 5. 显示项目结构
    run_command(
        "ls -la",
        "步骤5: 显示当前文件夹结构"
    )

    # 6. 显示Excel文件列表
    run_command(
        "ls -la *.xlsx",
        "步骤6: 显示所有Excel文件"
    )
    
    print(f"\n{'='*80}")
    print("🎉 演示完成！")
    print("=" * 80)
    print("✅ 所有功能都已成功验证：")
    print("   • Prompt内容解析 ✅")
    print("   • Output内容解析 ✅ (新增功能)")
    print("   • SessionID科学计数法修复 ✅")
    print("   • 文件管理优化 ✅")
    print("   • 结构化数据输出 ✅")
    print("   • 模型思考过程提取 ✅ (新增)")
    print("   • 模型回复内容提取 ✅ (新增)")
    print("   • 模型方案解析 ✅ (新增)")
    print("\n📁 所有文件都已保存在prompt_parser文件夹下")
    print("📖 详细使用说明请查看README.md")
    print("📋 项目总结请查看项目总结.md")

if __name__ == "__main__":
    main()
