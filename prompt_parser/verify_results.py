#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证解析结果的脚本
"""

import pandas as pd
import glob
import os

def verify_parsing_results():
    """验证解析结果"""
    # 查找最新的解析结果文件
    pattern = "../*解析结果*.xlsx"
    files = glob.glob(pattern)
    
    if not files:
        print("未找到解析结果文件")
        return
    
    # 选择最新的文件
    latest_file = max(files, key=os.path.getctime)
    print(f"验证文件: {latest_file}")
    
    try:
        df = pd.read_excel(latest_file)
        
        print(f"\n文件基本信息:")
        print(f"总行数: {len(df)}")
        print(f"总列数: {len(df.columns)}")
        print(f"列名: {list(df.columns)}")
        
        # 检查新增的列
        new_columns = ['业务知识', '系统信号', '外呼通话记录', '历史上下文', '处理后的Prompt']
        
        print(f"\n新增列验证:")
        for col in new_columns:
            if col in df.columns:
                non_empty = df[df[col].str.strip() != ""][col].count()
                print(f"✅ {col}: {non_empty} 行有内容")
            else:
                print(f"❌ {col}: 列不存在")
        
        # 显示第一行的解析结果示例
        if len(df) > 0:
            print(f"\n第一行解析结果示例:")
            print("="*60)
            
            for col in new_columns:
                if col in df.columns and pd.notna(df.iloc[0][col]):
                    content = str(df.iloc[0][col])
                    print(f"\n【{col}】:")
                    # 显示前200字符
                    if len(content) > 200:
                        print(content[:200] + "...")
                    else:
                        print(content)
                else:
                    print(f"\n【{col}】: (空)")
            
            print("="*60)
        
        # 检查¥¥替换是否成功
        if '处理后的Prompt' in df.columns:
            first_processed = str(df.iloc[0]['处理后的Prompt']) if pd.notna(df.iloc[0]['处理后的Prompt']) else ""
            yuan_count = first_processed.count('¥¥')
            newline_count = first_processed.count('\n')
            
            print(f"\n符号替换验证:")
            print(f"处理后的Prompt中剩余'¥¥': {yuan_count} 个")
            print(f"处理后的Prompt中换行符: {newline_count} 个")
            
            if yuan_count == 0:
                print("✅ ¥¥替换成功")
            else:
                print("⚠️  仍有¥¥未替换")
        
    except Exception as e:
        print(f"验证过程中出错: {e}")

if __name__ == "__main__":
    verify_parsing_results()
