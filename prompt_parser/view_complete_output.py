#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看完整output内容的脚本
"""

import pandas as pd
import json

def view_complete_output():
    """查看完整的output内容"""
    try:
        df = pd.read_excel("Aida-node_88294559_20250812.xlsx", dtype={'session_id': str})
        
        if 'output' in df.columns:
            # 获取第一个非空的output
            first_output = df[df['output'].notna()]['output'].iloc[0]
            
            print("完整的Output内容:")
            print("="*80)
            print(first_output)
            print("="*80)
            
            # 尝试解析JSON
            try:
                parsed_json = json.loads(first_output)
                print("\n解析后的JSON结构:")
                print(json.dumps(parsed_json, indent=2, ensure_ascii=False))
                
                # 查看嵌套的JSON
                if 'CallActivity_1y4djge__complete_content_' in parsed_json:
                    nested_content = parsed_json['CallActivity_1y4djge__complete_content_']
                    print(f"\n嵌套内容类型: {type(nested_content)}")
                    
                    if isinstance(nested_content, str):
                        print("\n尝试解析嵌套的JSON字符串:")
                        try:
                            nested_json = json.loads(nested_content)
                            print(json.dumps(nested_json, indent=2, ensure_ascii=False))
                            
                            print(f"\n嵌套JSON包含的字段:")
                            for key in nested_json.keys():
                                print(f"  - {key}")
                                
                        except json.JSONDecodeError as e:
                            print(f"嵌套JSON解析失败: {e}")
                            print("嵌套内容:")
                            print(nested_content)
                    else:
                        print("嵌套内容已经是字典格式:")
                        print(json.dumps(nested_content, indent=2, ensure_ascii=False))
                        
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
                
    except Exception as e:
        print(f"处理出错: {e}")

if __name__ == "__main__":
    view_complete_output()
