#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析output列结构的脚本
用于了解output列中JSON数据的格式
"""

import pandas as pd
import json
import re

def analyze_output_structure():
    """分析output列的结构"""
    try:
        # 读取Excel文件
        df = pd.read_excel("Aida-node_88294559_20250812.xlsx", dtype={'session_id': str})
        
        print(f"Excel文件基本信息:")
        print(f"总行数: {len(df)}")
        print(f"列名: {list(df.columns)}")
        
        if 'output' in df.columns:
            print(f"\nOutput列分析:")
            non_empty_outputs = df[df['output'].notna()]['output']
            print(f"非空output数量: {len(non_empty_outputs)}")
            
            # 显示前3个output示例
            for i, output in enumerate(non_empty_outputs.head(3)):
                print(f"\n--- Output示例 {i+1} ---")
                print(f"长度: {len(str(output))} 字符")
                
                # 尝试解析JSON
                try:
                    if isinstance(output, str):
                        # 清理可能的格式问题
                        cleaned_output = output.strip()
                        if cleaned_output.startswith('```json'):
                            cleaned_output = cleaned_output.replace('```json', '').replace('```', '').strip()
                        
                        parsed_json = json.loads(cleaned_output)
                        print("JSON解析成功！")
                        print("包含的字段:")
                        for key in parsed_json.keys():
                            value = parsed_json[key]
                            if isinstance(value, str) and len(value) > 100:
                                print(f"  - {key}: {value[:100]}...")
                            else:
                                print(f"  - {key}: {value}")
                    else:
                        print("Output不是字符串格式")
                        print(f"类型: {type(output)}")
                        print(f"内容: {str(output)[:200]}...")
                        
                except json.JSONDecodeError as e:
                    print(f"JSON解析失败: {e}")
                    print("原始内容前200字符:")
                    print(str(output)[:200])
                except Exception as e:
                    print(f"处理出错: {e}")
                    print("原始内容前200字符:")
                    print(str(output)[:200])
        else:
            print("未找到output列")
            
    except Exception as e:
        print(f"分析过程中出错: {e}")

if __name__ == "__main__":
    analyze_output_structure()
