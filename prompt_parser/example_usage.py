#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prompt解析器使用示例

这个脚本展示了如何使用PromptParser类进行各种操作
"""

from prompt_parser import PromptParser
import pandas as pd

def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 创建解析器实例
    parser = PromptParser()
    
    # 处理Excel文件
    input_file = "../Aida-node_88294559_20250812.xlsx"
    output_file = "../示例输出_解析结果.xlsx"
    
    result = parser.process_excel(input_file, output_file)
    
    if result:
        print(f"✅ 处理成功！输出文件: {result}")
    else:
        print("❌ 处理失败！")

def example_single_prompt_parsing():
    """单个Prompt解析示例"""
    print("\n=== 单个Prompt解析示例 ===")
    
    # 示例Prompt文本（简化版）
    sample_prompt = """
    你是客服助理¥¥可能用到的业务知识如下：¥¥1.当用户反馈问题时...¥¥可能用到的方案列表如下：¥¥方案A¥¥系统信号如下：¥¥{时间:2025}¥¥客服和商家或者骑手的最近一次通话记录如下:¥¥无通话记录¥¥对话历史如下：¥¥用户:有问题¥¥客服:好的
    """
    
    parser = PromptParser()
    result = parser.parse_prompt(sample_prompt)
    
    print("解析结果:")
    for key, value in result.items():
        print(f"{key}: {value[:50]}..." if len(value) > 50 else f"{key}: {value}")

def example_batch_processing():
    """批量处理示例"""
    print("\n=== 批量处理示例 ===")
    
    # 模拟批量处理多个文件
    files_to_process = [
        "../Aida-node_88294559_20250812.xlsx",
        # 可以添加更多文件
    ]
    
    parser = PromptParser()
    
    for file_path in files_to_process:
        try:
            print(f"处理文件: {file_path}")
            result = parser.process_excel(file_path)
            if result:
                print(f"✅ 成功: {result}")
            else:
                print(f"❌ 失败: {file_path}")
        except Exception as e:
            print(f"❌ 错误: {file_path} - {e}")

def example_custom_analysis():
    """自定义分析示例"""
    print("\n=== 自定义分析示例 ===")
    
    # 读取解析结果进行进一步分析
    try:
        # 查找最新的解析结果文件
        import glob
        pattern = "../*解析结果*.xlsx"
        files = glob.glob(pattern)
        
        if files:
            latest_file = max(files, key=lambda x: x)
            df = pd.read_excel(latest_file)
            
            print(f"分析文件: {latest_file}")
            print(f"总数据量: {len(df)} 行")
            
            # 分析业务知识分布
            if '业务知识' in df.columns:
                business_knowledge_stats = df['业务知识'].str.len().describe()
                print(f"\n业务知识长度统计:")
                print(business_knowledge_stats)
            
            # 分析系统信号
            if '系统信号' in df.columns:
                signal_with_content = df[df['系统信号'].str.strip() != '']['系统信号'].count()
                print(f"\n包含系统信号的行数: {signal_with_content}")
            
            # 分析对话历史
            if '历史上下文' in df.columns:
                avg_dialogue_length = df['历史上下文'].str.len().mean()
                print(f"平均对话历史长度: {avg_dialogue_length:.1f} 字符")
        
        else:
            print("未找到解析结果文件")
            
    except Exception as e:
        print(f"分析过程中出错: {e}")

def main():
    """主函数 - 运行所有示例"""
    print("Prompt解析器使用示例")
    print("=" * 50)
    
    # 运行各种示例
    example_basic_usage()
    example_single_prompt_parsing()
    example_batch_processing()
    example_custom_analysis()
    
    print("\n" + "=" * 50)
    print("所有示例运行完成！")

if __name__ == "__main__":
    main()
