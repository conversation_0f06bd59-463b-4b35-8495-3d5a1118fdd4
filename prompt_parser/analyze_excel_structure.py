#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件结构分析脚本
用于分析Excel文件中的Prompt列内容结构
"""

import pandas as pd
import sys
import os

def analyze_excel_structure(excel_path):
    """分析Excel文件结构"""
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path)
        
        print(f"Excel文件: {excel_path}")
        print(f"总行数: {len(df)}")
        print(f"总列数: {len(df.columns)}")
        print(f"列名: {list(df.columns)}")
        print("\n" + "="*50)
        
        # 查找Prompt列
        prompt_columns = [col for col in df.columns if 'prompt' in col.lower() or 'Prompt' in col]
        
        if prompt_columns:
            print(f"找到Prompt相关列: {prompt_columns}")
            
            for col in prompt_columns:
                print(f"\n分析列: {col}")
                print(f"非空值数量: {df[col].notna().sum()}")
                
                # 显示前几个非空的Prompt内容示例
                non_empty = df[df[col].notna()][col]
                if len(non_empty) > 0:
                    print(f"\n前3个Prompt内容示例:")
                    for i, prompt in enumerate(non_empty.head(3)):
                        print(f"\n--- 示例 {i+1} ---")
                        print(f"长度: {len(str(prompt))} 字符")
                        # 显示前500字符
                        print(str(prompt)[:500] + "..." if len(str(prompt)) > 500 else str(prompt))
        else:
            print("未找到Prompt相关列")
            print("所有列名:")
            for col in df.columns:
                print(f"  - {col}")
                
    except Exception as e:
        print(f"分析Excel文件时出错: {e}")

if __name__ == "__main__":
    excel_path = "../Aida-node_88294559_20250812.xlsx"
    if len(sys.argv) > 1:
        excel_path = sys.argv[1]
    
    if os.path.exists(excel_path):
        analyze_excel_structure(excel_path)
    else:
        print(f"文件不存在: {excel_path}")
