#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证SessionID格式的脚本
检查是否修复了科学计数法问题
"""

import pandas as pd
import glob
import os

def verify_session_id():
    """验证SessionID格式"""
    # 查找最新的解析结果文件
    pattern = "*解析结果*.xlsx"
    files = glob.glob(pattern)
    
    if not files:
        print("未找到解析结果文件")
        return
    
    # 选择最新的文件
    latest_file = max(files, key=os.path.getctime)
    print(f"验证文件: {latest_file}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(latest_file, dtype={'session_id': str})
        
        print(f"\n文件基本信息:")
        print(f"总行数: {len(df)}")
        print(f"总列数: {len(df.columns)}")
        
        # 检查session_id列
        if 'session_id' in df.columns:
            print(f"\nSessionID格式验证:")
            
            # 显示前10个session_id
            print("前10个SessionID:")
            for i, session_id in enumerate(df['session_id'].head(10)):
                print(f"  {i+1}: {session_id} (类型: {type(session_id)})")
            
            # 检查是否有科学计数法
            scientific_notation_count = 0
            for session_id in df['session_id']:
                if 'e+' in str(session_id).lower() or 'e-' in str(session_id).lower():
                    scientific_notation_count += 1
            
            print(f"\n科学计数法检查:")
            print(f"包含科学计数法的SessionID数量: {scientific_notation_count}")
            
            if scientific_notation_count == 0:
                print("✅ SessionID科学计数法问题已修复")
            else:
                print("⚠️  仍有SessionID使用科学计数法")
                
            # 检查SessionID长度分布
            lengths = df['session_id'].str.len()
            print(f"\nSessionID长度统计:")
            print(f"最短长度: {lengths.min()}")
            print(f"最长长度: {lengths.max()}")
            print(f"平均长度: {lengths.mean():.1f}")
            
        else:
            print("❌ 未找到session_id列")
        
        # 检查文件保存位置
        current_dir = os.getcwd()
        file_dir = os.path.dirname(os.path.abspath(latest_file))
        
        print(f"\n文件位置验证:")
        print(f"当前目录: {current_dir}")
        print(f"文件目录: {file_dir}")
        
        if current_dir == file_dir:
            print("✅ 文件已保存在prompt_parser文件夹下")
        else:
            print("⚠️  文件未保存在prompt_parser文件夹下")
            
    except Exception as e:
        print(f"验证过程中出错: {e}")

if __name__ == "__main__":
    verify_session_id()
